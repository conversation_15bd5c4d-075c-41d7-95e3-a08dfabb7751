defmodule Ecto.Relation.Schema.Serializable do
  defmacro __using__(_opts) do
    quote location: :keep do
      defimpl JSON.Encoder, for: __MODULE__ do
        def encode(component, opts) do
          JSON.Encoder.encode(
            %{
              __struct__: component.__struct__.name(),
              attributes: Ecto.Relation.Schema.Serializable.Dumper.dump(component)
            },
            opts
          )
        end
      end

      def name, do: __MODULE__ |> to_string() |> String.split(".") |> List.last()

      def load(json), do: Ecto.Relation.Schema.Serializable.Loader.load(json, __MODULE__)
    end
  end

  defprotocol Loader do
    @fallback_to_any true

    @spec load(any()) :: any()
    def load(value)

    @spec load(any(), module()) :: struct()
    def load(value, module)
  end

  defprotocol Dumper do
    @fallback_to_any true

    @spec dump(struct()) :: map()
    def dump(component)

    @spec dump(atom(), any()) :: any()
    def dump(key, value)
  end
end

defimpl Ecto.Relation.Schema.Serializable.Dumper, for: Map do
  def dump(map) do
    Enum.reduce(map, %{}, fn {key, value}, acc ->
      Map.put(acc, key, @protocol.dump(key, value))
    end)
  end

  def dump(_key, map), do: map
end

defimpl Ecto.Relation.Schema.Serializable.Dumper, for: Any do
  def dump(_key, value), do: value

  def dump(struct) when is_struct(struct) do
    @protocol.dump(Map.from_struct(struct))
  end

  def dump(value) when is_atom(value) do
    [:atom, value]
  end

  def dump(value), do: value
end

defimpl Ecto.Relation.Schema.Serializable.Loader, for: Map do
  def load(map) when not is_map_key(map, "__struct__") do
    Enum.reduce(map, %{}, fn {key, value}, acc ->
      Map.put(acc, String.to_atom(key), @protocol.load(value))
    end)
  end

  def load(%{"__struct__" => module, "attributes" => attributes}) when module != "Schema" do
    @protocol.load(attributes, Module.concat(Ecto.Relation.Schema, module))
  end

  def load(%{"attributes" => attributes}, module) do
    struct(module, @protocol.load(attributes))
  end

  def load(attributes, module) do
    struct(module, @protocol.load(attributes))
  end
end

defimpl Ecto.Relation.Schema.Serializable.Loader, for: Atom do
  def load(value), do: [:atom, value]
  def load(value, _), do: [:atom, value]
end

defimpl Ecto.Relation.Schema.Serializable.Loader, for: List do
  def load(["atom", value]), do: String.to_atom(value)
  def load(list), do: Enum.map(list, &@protocol.load(&1))
  def load(list, module), do: Enum.map(list, &@protocol.load(&1, module))
end

defimpl Ecto.Relation.Schema.Serializable.Loader, for: Any do
  def load(value), do: value
  def load(value, _module), do: value
end
