{application,drops,
             [{modules,['Elixir.Drops.Application','Elixir.Drops.Casters',
                        'Elixir.Drops.Config','Elixir.Drops.Contract',
                        'Elixir.Drops.Operations',
                        'Elixir.Drops.Operations.Command',
                        'Elixir.Drops.Operations.Extension',
                        'Elixir.Drops.Operations.Extensions.Command',
                        'Elixir.Drops.Operations.Extensions.Ecto',
                        'Elixir.Drops.Operations.Extensions.Params',
                        'Elixir.Drops.Operations.Extensions.Telemetry',
                        'Elixir.Drops.Operations.Trace',
                        'Elixir.Drops.Operations.UnitOfWork',
                        'Elixir.Drops.Predicates',
                        'Elixir.Drops.Predicates.Helpers',
                        'Elixir.Drops.Relation',
                        'Elixir.Drops.Relation.Composite',
                        'Elixir.Drops.Relation.Inference',
                        'Elixir.Drops.Relation.Inference.FieldCandidate',
                        'Elixir.Drops.Relation.Inference.FieldCandidates',
                        'Elixir.Drops.Relation.Inference.SchemaFieldAST',
                        'Elixir.Drops.Relation.Inference.SchemaFieldAST.Drops.Relation.Schema.Field',
                        'Elixir.Drops.Relation.Query',
                        'Elixir.Drops.Relation.SQL.Database',
                        'Elixir.Drops.Relation.SQL.Database.Column',
                        'Elixir.Drops.Relation.SQL.Database.ForeignKey',
                        'Elixir.Drops.Relation.SQL.Database.Index',
                        'Elixir.Drops.Relation.SQL.Database.PrimaryKey',
                        'Elixir.Drops.Relation.SQL.Database.Table',
                        'Elixir.Drops.Relation.SQL.DatabaseInference',
                        'Elixir.Drops.Relation.SQL.DatabaseToSchema',
                        'Elixir.Drops.Relation.SQL.DatabaseToSchema.Drops.Relation.SQL.Database.Column',
                        'Elixir.Drops.Relation.SQL.DatabaseToSchema.Drops.Relation.SQL.Database.ForeignKey',
                        'Elixir.Drops.Relation.SQL.DatabaseToSchema.Drops.Relation.SQL.Database.Index',
                        'Elixir.Drops.Relation.SQL.DatabaseToSchema.Drops.Relation.SQL.Database.PrimaryKey',
                        'Elixir.Drops.Relation.SQL.Inference',
                        'Elixir.Drops.Relation.SQL.Introspector',
                        'Elixir.Drops.Relation.SQL.Introspector.Database',
                        'Elixir.Drops.Relation.SQL.Introspector.Database.Postgres',
                        'Elixir.Drops.Relation.SQL.Introspector.Database.SQLite',
                        'Elixir.Drops.Relation.Schema',
                        'Elixir.Drops.Relation.Schema.Field',
                        'Elixir.Drops.Relation.Schema.ForeignKey',
                        'Elixir.Drops.Relation.Schema.Generator',
                        'Elixir.Drops.Relation.Schema.Index',
                        'Elixir.Drops.Relation.Schema.Indices',
                        'Elixir.Drops.Relation.Schema.MetadataExtractor',
                        'Elixir.Drops.Relation.Schema.PrimaryKey',
                        'Elixir.Drops.Relation.SchemaCache',
                        'Elixir.Drops.Schema','Elixir.Drops.Schema.Compiler',
                        'Elixir.Drops.Schema.Compiler.Atom',
                        'Elixir.Drops.Schema.Inference',
                        'Elixir.Drops.Schema.Inference.Atom',
                        'Elixir.Drops.Schema.Inference.List',
                        'Elixir.Drops.Schema.Inference.Map',
                        'Elixir.Drops.Type','Elixir.Drops.Type.Compiler',
                        'Elixir.Drops.Type.DSL','Elixir.Drops.Type.Validator',
                        'Elixir.Drops.Type.Validator.Drops.Types.Cast',
                        'Elixir.Drops.Type.Validator.Drops.Types.EctoCaster',
                        'Elixir.Drops.Type.Validator.Drops.Types.List',
                        'Elixir.Drops.Type.Validator.Drops.Types.Map',
                        'Elixir.Drops.Type.Validator.Drops.Types.Map.Key',
                        'Elixir.Drops.Type.Validator.Drops.Types.Number',
                        'Elixir.Drops.Type.Validator.Drops.Types.Primitive',
                        'Elixir.Drops.Type.Validator.Drops.Types.Union',
                        'Elixir.Drops.Types.Cast',
                        'Elixir.Drops.Types.EctoCaster',
                        'Elixir.Drops.Types.List','Elixir.Drops.Types.Map',
                        'Elixir.Drops.Types.Map.Key',
                        'Elixir.Drops.Types.Map.Validator',
                        'Elixir.Drops.Types.Number',
                        'Elixir.Drops.Types.Primitive',
                        'Elixir.Drops.Types.Union',
                        'Elixir.Drops.Types.Union.Validator',
                        'Elixir.Drops.Validator.Messages.Backend',
                        'Elixir.Drops.Validator.Messages.DefaultBackend',
                        'Elixir.Drops.Validator.Messages.Error',
                        'Elixir.Drops.Validator.Messages.Error.Caster',
                        'Elixir.Drops.Validator.Messages.Error.Conversions',
                        'Elixir.Drops.Validator.Messages.Error.Conversions.Drops.Validator.Messages.Error.Caster',
                        'Elixir.Drops.Validator.Messages.Error.Conversions.Drops.Validator.Messages.Error.Key',
                        'Elixir.Drops.Validator.Messages.Error.Conversions.Drops.Validator.Messages.Error.Rule',
                        'Elixir.Drops.Validator.Messages.Error.Conversions.Drops.Validator.Messages.Error.Set',
                        'Elixir.Drops.Validator.Messages.Error.Conversions.Drops.Validator.Messages.Error.Type',
                        'Elixir.Drops.Validator.Messages.Error.Conversions.Drops.Validator.Messages.Error.Union',
                        'Elixir.Drops.Validator.Messages.Error.Conversions.List',
                        'Elixir.Drops.Validator.Messages.Error.Key',
                        'Elixir.Drops.Validator.Messages.Error.Rule',
                        'Elixir.Drops.Validator.Messages.Error.Set',
                        'Elixir.Drops.Validator.Messages.Error.Type',
                        'Elixir.Drops.Validator.Messages.Error.Union',
                        'Elixir.Ecto.Queryable.Drops.Relation.Composite',
                        'Elixir.Enumerable.Drops.Relation.Composite',
                        'Elixir.Enumerable.Drops.Relation.Schema',
                        'Elixir.Inspect.Drops.Relation.Schema',
                        'Elixir.Inspect.Drops.Relation.Schema.Field',
                        'Elixir.Inspect.Drops.Relation.Schema.ForeignKey',
                        'Elixir.Inspect.Drops.Relation.Schema.Index',
                        'Elixir.Inspect.Drops.Relation.Schema.Indices',
                        'Elixir.Inspect.Drops.Relation.Schema.PrimaryKey',
                        'Elixir.Mix.Tasks.Drops.Dev.Setup',
                        'Elixir.Mix.Tasks.Drops.Example',
                        'Elixir.Mix.Tasks.Drops.Relations.GenSchemas',
                        'Elixir.Mix.Tasks.Test.Group',
                        'Elixir.String.Chars.Drops.Validator.Messages.Error.Caster',
                        'Elixir.String.Chars.Drops.Validator.Messages.Error.Key',
                        'Elixir.String.Chars.Drops.Validator.Messages.Error.Rule',
                        'Elixir.String.Chars.Drops.Validator.Messages.Error.Set',
                        'Elixir.String.Chars.Drops.Validator.Messages.Error.Type',
                        'Elixir.String.Chars.Drops.Validator.Messages.Error.Union']},
              {optional_applications,[igniter,ecto,ecto_sql,ecto_range]},
              {applications,[kernel,stdlib,elixir,logger,nimble_options,
                             telemetry,igniter,ecto,ecto_sql,ecto_range,
                             postgrex]},
              {description,"Tools for working with data effectively - data contracts using types, schemas, domain validation rules, type-safe casting, and more.\n"},
              {vsn,"0.2.1"},
              {mod,{'Elixir.Drops.Application',[]}},
              {registered,['Elixir.Drops.Supervisor']}]}.
