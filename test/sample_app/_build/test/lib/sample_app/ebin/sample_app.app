{application,sample_app,
             [{modules,['Elixir.Ecto.Queryable.Users',
                        'Elixir.Enumerable.Users','Elixir.SampleApp',
                        'Elixir.SampleApp.Repo','Elixir.Users',
                        'Elixir.Users.Struct']},
              {optional_applications,[igniter]},
              {applications,[kernel,stdlib,elixir,logger,ecto,ecto_sqlite3,
                             jason,igniter,drops]},
              {description,"sample_app"},
              {registered,[]},
              {vsn,"0.1.0"},
              {mod,{'Elixir.SampleApp',[]}}]}.
