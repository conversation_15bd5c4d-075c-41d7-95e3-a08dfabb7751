{<<"links">>,[{<<"GitHub">>,<<"https://github.com/elixir-ecto/postgrex">>}]}.
{<<"name">>,<<"postgrex">>}.
{<<"version">>,<<"0.20.0">>}.
{<<"description">>,<<"PostgreSQL driver for Elixir">>}.
{<<"elixir">>,<<"~> 1.11">>}.
{<<"files">>,
 [<<"lib">>,<<"lib/postgrex">>,<<"lib/postgrex/builtins.ex">>,
  <<"lib/postgrex/replication_connection.ex">>,<<"lib/postgrex/protocol.ex">>,
  <<"lib/postgrex/simple_connection.ex">>,<<"lib/postgrex/stream.ex">>,
  <<"lib/postgrex/types.ex">>,<<"lib/postgrex/type_module.ex">>,
  <<"lib/postgrex/type_info.ex">>,<<"lib/postgrex/error.ex">>,
  <<"lib/postgrex/binary_extension.ex">>,<<"lib/postgrex/query.ex">>,
  <<"lib/postgrex/extensions">>,<<"lib/postgrex/extensions/bool.ex">>,
  <<"lib/postgrex/extensions/circle.ex">>,
  <<"lib/postgrex/extensions/ltree.ex">>,
  <<"lib/postgrex/extensions/polygon.ex">>,
  <<"lib/postgrex/extensions/range.ex">>,
  <<"lib/postgrex/extensions/line.ex">>,
  <<"lib/postgrex/extensions/timetz.ex">>,
  <<"lib/postgrex/extensions/void_binary.ex">>,
  <<"lib/postgrex/extensions/int4.ex">>,<<"lib/postgrex/extensions/xid8.ex">>,
  <<"lib/postgrex/extensions/line_segment.ex">>,
  <<"lib/postgrex/extensions/timestamptz.ex">>,
  <<"lib/postgrex/extensions/float8.ex">>,
  <<"lib/postgrex/extensions/lquery.ex">>,
  <<"lib/postgrex/extensions/time.ex">>,<<"lib/postgrex/extensions/oid.ex">>,
  <<"lib/postgrex/extensions/box.ex">>,
  <<"lib/postgrex/extensions/tsvector.ex">>,
  <<"lib/postgrex/extensions/inet.ex">>,<<"lib/postgrex/extensions/raw.ex">>,
  <<"lib/postgrex/extensions/interval.ex">>,
  <<"lib/postgrex/extensions/jsonb.ex">>,
  <<"lib/postgrex/extensions/array.ex">>,<<"lib/postgrex/extensions/tid.ex">>,
  <<"lib/postgrex/extensions/timestamp.ex">>,
  <<"lib/postgrex/extensions/name.ex">>,<<"lib/postgrex/extensions/uuid.ex">>,
  <<"lib/postgrex/extensions/path.ex">>,<<"lib/postgrex/extensions/date.ex">>,
  <<"lib/postgrex/extensions/int8.ex">>,<<"lib/postgrex/extensions/json.ex">>,
  <<"lib/postgrex/extensions/hstore.ex">>,
  <<"lib/postgrex/extensions/macaddr.ex">>,
  <<"lib/postgrex/extensions/float4.ex">>,
  <<"lib/postgrex/extensions/point.ex">>,
  <<"lib/postgrex/extensions/numeric.ex">>,
  <<"lib/postgrex/extensions/int2.ex">>,
  <<"lib/postgrex/extensions/void_text.ex">>,
  <<"lib/postgrex/extensions/record.ex">>,
  <<"lib/postgrex/extensions/bit_string.ex">>,
  <<"lib/postgrex/extensions/multirange.ex">>,<<"lib/postgrex/errcodes.txt">>,
  <<"lib/postgrex/scram.ex">>,<<"lib/postgrex/error_code.ex">>,
  <<"lib/postgrex/scram">>,<<"lib/postgrex/scram/locked_cache.ex">>,
  <<"lib/postgrex/default_types.ex">>,<<"lib/postgrex/result.ex">>,
  <<"lib/postgrex/type_supervisor.ex">>,<<"lib/postgrex/utils.ex">>,
  <<"lib/postgrex/type_server.ex">>,<<"lib/postgrex/extension.ex">>,
  <<"lib/postgrex/notifications.ex">>,<<"lib/postgrex/binary_utils.ex">>,
  <<"lib/postgrex/super_extension.ex">>,<<"lib/postgrex/messages.ex">>,
  <<"lib/postgrex/parameters.ex">>,<<"lib/postgrex/app.ex">>,
  <<"lib/postgrex.ex">>,<<".formatter.exs">>,<<"mix.exs">>,<<"README.md">>,
  <<"CHANGELOG.md">>]}.
{<<"app">>,<<"postgrex">>}.
{<<"licenses">>,[<<"Apache-2.0">>]}.
{<<"requirements">>,
 [[{<<"name">>,<<"jason">>},
   {<<"app">>,<<"jason">>},
   {<<"optional">>,true},
   {<<"requirement">>,<<"~> 1.0">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"table">>},
   {<<"app">>,<<"table">>},
   {<<"optional">>,true},
   {<<"requirement">>,<<"~> 0.1.0">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"decimal">>},
   {<<"app">>,<<"decimal">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 1.5 or ~> 2.0">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"db_connection">>},
   {<<"app">>,<<"db_connection">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 2.1">>},
   {<<"repository">>,<<"hexpm">>}]]}.
{<<"build_tools">>,[<<"mix">>]}.
